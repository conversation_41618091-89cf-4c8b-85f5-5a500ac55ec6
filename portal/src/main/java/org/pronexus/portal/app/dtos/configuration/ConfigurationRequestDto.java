package org.pronexus.portal.app.dtos.configuration;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigurationRequestDto {
    
    @NotBlank(message = "Key is required")
    @Size(max = 100, message = "Key must be less than 100 characters")
    private String key;
    
    @NotBlank(message = "Value is required")
    private String value;
    
    @Size(max = 500, message = "Description must be less than 500 characters")
    private String description;
}
