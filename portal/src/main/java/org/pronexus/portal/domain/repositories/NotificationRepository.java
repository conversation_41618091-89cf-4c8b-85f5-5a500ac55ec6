package org.pronexus.portal.domain.repositories;

import org.pronexus.portal.domain.entities.Notification;
import org.pronexus.portal.domain.entities.type.NotificationEventType;
import org.pronexus.portal.domain.entities.type.NotificationStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NotificationRepository extends JpaRepository<Notification, Integer>, JpaSpecificationExecutor<Notification> {
    Notification findNotificationByEvent(NotificationEventType event);
    Notification findNotificationByEventAndStatus(NotificationEventType event, NotificationStatus status);
    Notification findNotificationById(Integer id);
    Notification findNotificationByIdAndStatus(Integer id, NotificationStatus status);

    List<Notification> findNotificationsByIdIn(List<Integer> ids);
}
