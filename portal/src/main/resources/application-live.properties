spring.application.name=portal
server.servlet.context-path=/portal

logging.config=classpath:logback-spring.xml

server.port=8000

spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://auth.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/certs

spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.default_schema=portal
spring.jpa.show-sql=true
spring.datasource.url=*****************************************************
spring.datasource.username=postgres
spring.datasource.password=Thanhnx@123$
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=200
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=HikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000

integration.service.url=https://api.pronexus.vn/integration
salary-advance.service.url=https://api.pronexus.vn/salary-advance
#salary-advance.service.url=http://localhost:8089/salary-advance
user.service.url=https://api.pronexus.vn/user
#user.service.url=http://localhost:8001/user

cors.allowed-origins=*
logging.level.org.hibernate.SQL=DEBUG
spring.main.allow-bean-definition-overriding=true

spring.output.ansi.enabled=ALWAYS

firebase.google-credentials=firebase-adminsdk.json
