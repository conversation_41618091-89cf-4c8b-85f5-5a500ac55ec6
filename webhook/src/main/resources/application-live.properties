server.port=8092
server.servlet.context-path=/webhook

spring.application.name=webhook
spring.threads.virtual.enabled=true

management.tracing.sampling.probability=1.0
management.endpoints.web.exposure.include=prometheus
management.metrics.distribution.percentiles-histogram.http.server.requests=true
management.metrics.tags.application=${spring.application.name}

logging.pattern.level=%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]

yas.services.webhook=https://api.pronexus.vn/webhook

spring.security.oauth2.resourceserver.jwt.issuer-uri=https://auth.pronexus.vn/realms/Yas

spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=*******************************************
spring.datasource.username=postgres
spring.datasource.password=Thanhnx@123$

# The SQL dialect makes Hibernate generate better SQL for the chosen database
spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.PostgreSQLDialect

# Hibernate ddl auto (none, create, create-drop, validate, update)
spring.jpa.hibernate.ddl-auto = none

#Enable liquibase
spring.liquibase.enabled=true

spring.kafka.bootstrap-servers=kafka:9092
spring.kafka.consumer.group-id=webhook
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.ByteArrayDeserializer

spring.kafka.consumer.properties.spring.json.use.type.headers=false

webhook.integration.kafka.product.topic-name=dbproduct.public.product
webhook.integration.kafka.order.topic-name=dborder.public.order

# swagger-ui custom path
springdoc.swagger-ui.path=/swagger-ui
springdoc.packagesToScan=com.yas.webhook
springdoc.swagger-ui.oauth.use-pkce-with-authorization-code-grant=true
springdoc.swagger-ui.oauth.client-id=swagger-ui
springdoc.oauthflow.authorization-url=https://auth.pronexus.vn/realms/Yas/protocol/openid-connect/auth
springdoc.oauthflow.token-url=https://auth.pronexus.vn/realms/Yas/protocol/openid-connect/token

spring.jpa.open-in-view=false
cors.allowed-origins=*
