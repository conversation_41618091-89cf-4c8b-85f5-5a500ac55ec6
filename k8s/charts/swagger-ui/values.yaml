# Default values for swagger-ui.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: swaggerapi/swagger-ui
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "v4.16.0"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

baseUrl: /swagger-ui
urls: "[{ url: 'http://api.yas.local.com/product/v3/api-docs', name: 'Product' },
      { url: 'http://api.yas.local.com/media/v3/api-docs', name: 'Media' },
      { url: 'http://api.yas.local.com/customer/v3/api-docs', name: 'Customer' },
      { url: 'http://api.yas.local.com/cart/v3/api-docs', name: 'Cart'},
      { url: 'http://api.yas.local.com/rating/v3/api-docs', name: 'Rating' },
      { url: 'http://api.yas.local.com/order/v3/api-docs', name: 'Order'},
      { url: 'http://api.yas.local.com/payment/v3/api-docs', name: 'Payment'},
      { url: 'http://api.yas.local.com/location/v3/api-docs', name: 'Location'},
      { url: 'http://api.yas.local.com/inventory/v3/api-docs', name: 'Inventory'},
      { url: 'http://api.yas.local.com/tax/v3/api-docs', name: 'Tax' },
      { url: 'http://api.yas.local.com/promotion/v3/api-docs', name: 'Promotion'}]"

service:
  type: ClusterIP
  port: 8080

ingress:
  enabled: true
  className: "nginx"
  host: api.yas.local.com
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: api.yas.local.com
      paths:
        - path: /swagger-ui
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
