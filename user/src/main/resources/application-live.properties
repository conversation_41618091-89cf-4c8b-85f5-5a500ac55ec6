spring.application.name=user
server.servlet.context-path=/user

logging.config=classpath:logback-spring.xml

server.port=8001

spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://auth.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/certs

spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.generate-ddl=true
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.globally_quoted_identifiers=false
spring.jpa.open-in-view=false

# Hibernate naming strategy - S? d?ng Spring Boot default ?? t? ??ng chuy?n camelCase -> snake_case
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
spring.jpa.hibernate.naming.implicit-strategy=org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl
spring.datasource.url=*****************************************************
spring.datasource.username=postgres
spring.datasource.password=Thanhnx@123$
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=200
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=HikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000

keycloak.realm=pronexus_dev
keycloak.auth-server-url=https://auth.pronexus.vn

keycloak.master-username=admin
keycloak.master-password=admin
keycloak.master-client-id=admin-cli
keycloak.client-id=pronexus_dev
keycloak.client-secret=m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2

keycloak.admin.username=system-admin
keycloak.admin.password=123456

service.portal.url=https://api.pronexus.vn/portal
#service.portal.url=http://localhost:8000/portal
