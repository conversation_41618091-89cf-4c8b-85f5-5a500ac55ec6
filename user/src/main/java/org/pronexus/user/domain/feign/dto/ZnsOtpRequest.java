package org.pronexus.user.domain.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * DTO cho request gửi OTP qua Zalo ZNS
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZnsOtpRequest {
    
    /**
     * Số điện thoại người nhận
     */
    private String phone;
    
    /**
     * ID của template thông báo
     */
    @JsonProperty("template_id")
    private String templateId;
    
    /**
     * Dữ liệu để điền vào template
     */
    @JsonProperty("template_data")
    private Map<String, String> templateData;
    
    /**
     * ID để tracking request
     */
    @JsonProperty("tracking_id")
    private String trackingId;
}
