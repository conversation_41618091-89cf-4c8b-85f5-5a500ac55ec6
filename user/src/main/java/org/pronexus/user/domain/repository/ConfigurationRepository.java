package org.pronexus.user.domain.repository;

import org.pronexus.user.domain.entity.Configuration;
import org.pronexus.user.domain.entity.constants.ConfigurationStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ConfigurationRepository extends JpaRepository<Configuration, String> {
    
    /**
     * T<PERSON>m cấu hình theo key và status
     */
    Optional<Configuration> findByKeyAndStatus(String key, ConfigurationStatus status);
}
