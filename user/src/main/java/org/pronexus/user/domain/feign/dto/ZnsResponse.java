package org.pronexus.user.domain.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO cho response từ Zalo ZNS
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZnsResponse {

    /**
     * Mã lỗi (0 = thành công)
     */
    private Integer error;

    /**
     * Thông báo
     */
    private String message;

    /**
     * Dữ liệu trả về
     */
    private ZnsResponseData data;

    /**
     * Kiểm tra response có thành công không
     *
     * @return true nếu thành công, false nếu thất bại
     */
    public boolean isSuccess() {
        return error != null && error == 0;
    }

    /**
     * DTO cho data trong response từ Zalo ZNS
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ZnsResponseData {

        /**
         * Thời gian gửi
         */
        @JsonProperty("sent_time")
        private String sentTime;

        /**
         * Chế độ gửi
         */
        @JsonProperty("sending_mode")
        private String sendingMode;

        /**
         * Thông tin quota
         */
        private ZnsQuota quota;

        /**
         * ID tin nhắn
         */
        @JsonProperty("msg_id")
        private String msgId;
    }

    /**
     * DTO cho quota trong response từ Zalo ZNS
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ZnsQuota {

        /**
         * Quota còn lại
         */
        @JsonProperty("remainingQuota")
        private String remainingQuota;

        /**
         * Quota hàng ngày
         */
        @JsonProperty("dailyQuota")
        private String dailyQuota;
    }
}
