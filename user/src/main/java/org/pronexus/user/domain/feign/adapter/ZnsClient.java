package org.pronexus.user.domain.feign.adapter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.user.domain.feign.ZnsFeignClient;
import org.pronexus.user.domain.feign.dto.ZnsOtpRequest;
import org.pronexus.user.domain.feign.dto.ZnsResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Adapter cho Zalo ZNS Feign Client
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ZnsClient {
    
    private final ZnsFeignClient znsFeignClient;
    
    @Value("${zns.api.key}")
    private String znsApiKey;
    
    /**
     * G<PERSON>i thông báo OTP qua Zalo ZNS
     * 
     * @param phoneNumber Số điện thoại người nhận
     * @param otpCode Mã OTP
     * @param templateId ID của template thông báo
     * @param otpExpiry Thời gian hết hạn của OTP (phút)
     * @return Kết quả gửi OTP
     */
    public ZnsResponse sendOtpNotification(String phoneNumber, String otpCode, String templateId, int otpExpiry) {
        try {
            // Chuẩn bị dữ liệu cho template
            Map<String, String> templateData = new HashMap<>();
            templateData.put("otp", otpCode);
            templateData.put("expiry", String.valueOf(otpExpiry));
            
            // Chuẩn bị request
            ZnsOtpRequest request = ZnsOtpRequest.builder()
                    .phone(phoneNumber)
                    .templateId(templateId)
                    .templateData(templateData)
                    .appId(znsAppId)
                    .build();
            
            // Gửi request
            ZnsResponse response = znsFeignClient.sendOtpNotification(znsApiKey, request);
            
            log.info("Đã gửi OTP qua ZNS đến số điện thoại: {}, kết quả: {}", phoneNumber, response);
            return response;
        } catch (Exception e) {
            log.error("Lỗi khi gửi OTP qua ZNS đến số điện thoại {}: {}", phoneNumber, e.getMessage(), e);
            throw new RuntimeException("Không thể gửi OTP qua ZNS: " + e.getMessage(), e);
        }
    }
}
