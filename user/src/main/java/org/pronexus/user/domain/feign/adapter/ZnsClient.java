package org.pronexus.user.domain.feign.adapter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.user.domain.feign.ZnsFeignClient;
import org.pronexus.user.domain.feign.dto.ZnsOtpRequest;
import org.pronexus.user.domain.feign.dto.ZnsResponse;
import org.pronexus.user.domain.service.core.ConfigurationService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Adapter cho Zalo ZNS Feign Client
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ZnsClient {

    private final ZnsFeignClient znsFeignClient;
    private final ConfigurationService configurationService;
    
    /**
     * G<PERSON>i thông báo OTP qua Zalo ZNS
     * 
     * @param phoneNumber Số điện thoại người nhận
     * @param otpCode Mã OTP
     * @param templateId ID của template thông báo
     * @param otpExpiry Thời gian hết hạn của OTP (phút)
     * @return Kết quả gửi OTP
     */
    public ZnsResponse sendOtpNotification(String phoneNumber, String otpCode, String templateId, int otpExpiry) {
        try {
            // Lấy access token từ database
            String accessToken = configurationService.getConfigurationValue("ZNS_ACCESS_TOKEN")
                    .orElseThrow(() -> new RuntimeException("Không tìm thấy ZNS_ACCESS_TOKEN trong cấu hình"));

            // Chuyển đổi số điện thoại sang format quốc tế
            String formattedPhoneNumber = formatPhoneNumber(phoneNumber);

            // Chuẩn bị dữ liệu cho template (chỉ có otp, không có expiry)
            Map<String, String> templateData = new HashMap<>();
            templateData.put("otp", otpCode);

            // Tạo tracking ID unique
            String trackingId = java.util.UUID.randomUUID().toString();

            // Chuẩn bị request
            ZnsOtpRequest request = ZnsOtpRequest.builder()
                    .phone(formattedPhoneNumber)
                    .templateId(templateId)
                    .templateData(templateData)
                    .trackingId(trackingId)
                    .build();

            // Gửi request
            ZnsResponse response = znsFeignClient.sendOtpNotification(accessToken, request);

            // Xử lý response thành công
            if (response.isSuccess()) {
                ZnsResponse.ZnsResponseData data = response.getData();
                if (data != null) {
                    log.info("Gửi OTP qua ZNS thành công - Phone: {} -> {}, Tracking ID: {}, Message ID: {}, Sent Time: {}, Remaining Quota: {}/{}",
                            phoneNumber, formattedPhoneNumber, trackingId,
                            data.getMsgId(), data.getSentTime(),
                            data.getQuota() != null ? data.getQuota().getRemainingQuota() : "N/A",
                            data.getQuota() != null ? data.getQuota().getDailyQuota() : "N/A");
                } else {
                    log.info("Gửi OTP qua ZNS thành công - Phone: {} -> {}, Tracking ID: {}, Response: {}",
                            phoneNumber, formattedPhoneNumber, trackingId, response.getMessage());
                }
            } else {
                log.warn("Gửi OTP qua ZNS thất bại - Phone: {} -> {}, Tracking ID: {}, Error: {}, Message: {}",
                        phoneNumber, formattedPhoneNumber, trackingId, response.getError(), response.getMessage());
            }

            return response;
        } catch (Exception e) {
            log.error("Lỗi khi gửi OTP qua ZNS đến số điện thoại {}: {}", phoneNumber, e.getMessage(), e);
            throw new RuntimeException("Không thể gửi OTP qua ZNS: " + e.getMessage(), e);
        }
    }

    /**
     * Chuyển đổi số điện thoại từ format Việt Nam sang format quốc tế
     * Ví dụ: 0987654321 -> 84987654321
     *
     * @param phoneNumber Số điện thoại đầu vào
     * @return Số điện thoại đã được format
     */
    private String formatPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return phoneNumber;
        }

        // Loại bỏ khoảng trắng và ký tự đặc biệt
        String cleanedPhone = phoneNumber.replaceAll("[\\s\\-\\(\\)\\+]", "");

        // Nếu số điện thoại bắt đầu bằng 0 và có 10 chữ số (format VN)
        if (cleanedPhone.startsWith("0") && cleanedPhone.length() == 10) {
            // Thay thế 0 đầu bằng 84
            return "84" + cleanedPhone.substring(1);
        }

        // Nếu đã có mã quốc gia 84 hoặc +84
        if (cleanedPhone.startsWith("84") && cleanedPhone.length() == 11) {
            return cleanedPhone;
        }

        // Nếu bắt đầu bằng +84
        if (cleanedPhone.startsWith("+84")) {
            return cleanedPhone.substring(1); // Bỏ dấu +
        }

        // Trường hợp khác, trả về số gốc
        log.warn("Số điện thoại không đúng format VN: {}", phoneNumber);
        return cleanedPhone;
    }
}
