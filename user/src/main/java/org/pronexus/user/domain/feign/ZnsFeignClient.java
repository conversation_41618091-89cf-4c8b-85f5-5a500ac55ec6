package org.pronexus.user.domain.feign;

import org.pronexus.user.domain.feign.dto.ZnsOtpRequest;
import org.pronexus.user.domain.feign.dto.ZnsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * Feign Client để gọi API của Zalo Notification Service (ZNS)
 */
@FeignClient(value = "zalo-notification-service", url = "${zns.api.url}")
public interface ZnsFeignClient {

    /**
     * G<PERSON>i thông báo OTP qua Zalo ZNS
     *
     * @param apiKey API key của Zalo ZNS
     * @param request Thông tin yêu cầu gửi OTP
     * @return Kết quả gửi OTP
     */
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    ZnsResponse sendOtpNotification(
            @RequestHeader(value = "access_token") String accessToken,
            @RequestBody ZnsOtpRequest request);
}
