package org.pronexus.user.domain.service.core;

/**
 * Interface định nghĩa các phương thức của ZnsService
 * Dùng để gửi thông báo qua Zalo Notification Service (ZNS)
 */
public interface ZnsService {
    
    /**
     * <PERSON><PERSON><PERSON> thông báo OTP qua Zalo
     * 
     * @param phoneNumber Số điện thoại người nhận (đã đăng ký Zalo)
     * @param otpCode Mã OTP
     * @param templateId ID của template thông báo
     * @param otpExpiry Thời gian hết hạn của OTP (phút)
     */
    void sendOtpNotification(String phoneNumber, String otpCode, String templateId, int otpExpiry);
    
    /**
     * G<PERSON>i thông báo tùy chỉnh qua Zalo
     * 
     * @param phoneNumber Số điện thoại người nhận (đã đăng ký <PERSON>alo)
     * @param templateId ID của template thông báo
     * @param templateData Dữ liệu để điền vào template
     */
    void sendCustomNotification(String phoneNumber, String templateId, Object templateData);
}
