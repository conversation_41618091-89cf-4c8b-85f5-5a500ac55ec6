package org.pronexus.user.domain.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.user.domain.entity.Configuration;
import org.pronexus.user.domain.entity.constants.ConfigurationStatus;
import org.pronexus.user.domain.repository.ConfigurationRepository;
import org.pronexus.user.domain.service.core.ConfigurationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ConfigurationServiceImpl implements ConfigurationService {

    private final ConfigurationRepository configurationRepository;

    @Override
    @Transactional(readOnly = true)
    public Optional<String> getConfigurationValue(String key) {
        try {
            Optional<Configuration> configOpt = configurationRepository.findByKeyAndStatus(key, ConfigurationStatus.ACTIVE);
            
            if (configOpt.isPresent()) {
                Configuration config = configOpt.get();
                if (config.getValue() != null) {
                    String value = config.getValue().getString("value");
                    log.debug("Lấy cấu hình thành công: key={}, value={}", key, value);
                    return Optional.ofNullable(value);
                }
            }
            
            log.warn("Không tìm thấy cấu hình với key: {}", key);
            return Optional.empty();
        } catch (Exception e) {
            log.error("Lỗi khi lấy cấu hình với key {}: {}", key, e.getMessage(), e);
            return Optional.empty();
        }
    }
}
