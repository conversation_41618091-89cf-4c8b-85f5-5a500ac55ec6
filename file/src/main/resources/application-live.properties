spring.application.name=file
server.servlet.context-path=/file

logging.config=classpath:logback-spring.xml

server.port=8002

spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://auth.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/certs

spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.generate-ddl=true
spring.jpa.show-sql=true
spring.datasource.url=*****************************************************
spring.datasource.username=postgres
spring.datasource.password=Thanhnx@123$
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=200
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=HikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000

file.directory=/images/

spring.servlet.multipart.max-file-size=10MB
cors.allowed-origins=*
