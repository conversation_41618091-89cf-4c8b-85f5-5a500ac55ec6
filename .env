
# Demo Platform
ENV_PLATFORM=local


# ******************
# Services
# ******************
# Kafka
KAFKA_SERVICE_HOST=kafka
KAFKA_SERVICE_PORT=9092
KAFKA_SERVICE_ADDR=kafka:${KAFKA_SERVICE_PORT}
KAFKA_BROKER_ID=1
KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://${KAFKA_SERVICE_ADDR},PLAINTEXT_HOST://kafka:29092
KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
KAFKA_INTER_BROKER_LISTENER_NAME=PLAINTEXT
KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1

# Postgres
POSTGRES_USER=admin
POSTGRES_PASSWORD=admin
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# ********************
# Telemetry Components
# ********************
# Grafana
GRAFANA_SERVICE_PORT=3000
GRAFANA_SERVICE_HOST=grafana

# Jaeger
JAEGER_SERVICE_PORT=16686
JAEGER_SERVICE_HOST=jaeger

# Prometheus
PROMETHEUS_SERVICE_PORT=9090
PROMETHEUS_SERVICE_HOST=prometheus
PROMETHEUS_ADDR=${PROMETHEUS_SERVICE_HOST}:${PROMETHEUS_SERVICE_PORT}

# Redis
SPRING_DATA_REDIS_HOST=redis
SPRING_DATA_REDIS_PORT=6379

# *******************************************************
# Environment Variables are injected to the microservices
# *******************************************************

# LOGGING_CONFIG=/app-config/logback-spring.xml

# The API Endpoint
PUBLIC_API_URL=https://api.pronexus.vn
SERVICES_CART=http://cart/cart
SERVICES_CUSTOMER=http://customer/customer
SERVICES_PRODUCT=http://product/product
SERVICES_TAX=http://tax/tax
SERVICES_ORDER=http://order/order
SERVICES_PAYMENT=http://payment/payment
SERVICES_LOCATION=http://location/location
SERVICES_MEDIA=http://media/media
SERVICES_PROMOTION=http://promotion/promotion
SERVICES_INVENTORY=http://inventory/inventory
SERVICES_RATING=http://rating/rating
SERVICES_SAMPLE_DATA=http://sampledata/sampledata
SERVICES_RECOMMENDATION=http://recommendation/recommendation
SERVER_PORT=80

# Swagger UI
URLS=[{ url: 'https://api.pronexus.vn/product/v3/api-docs', name: 'Product' },{ url: 'https://api.pronexus.vn/media/v3/api-docs', name: 'Media' },{ url: 'https://api.pronexus.vn/customer/v3/api-docs', name: 'Customer' },{ url: 'https://api.pronexus.vn/cart/v3/api-docs', name: 'Cart'},{ url: 'https://api.pronexus.vn/rating/v3/api-docs', name: 'Rating' }, { url: 'https://api.pronexus.vn/order/v3/api-docs', name: 'Order'},{ url: 'https://api.pronexus.vn/payment/v3/api-docs', name: 'Payment'},{ url: 'https://api.pronexus.vn/payment-paypal/v3/api-docs', name: 'Payment-paypal'},{ url: 'https://api.pronexus.vn/location/v3/api-docs', name: 'Location'}, { url: 'https://api.pronexus.vn/inventory/v3/api-docs', name: 'Inventory'},{ url: 'https://api.pronexus.vn/tax/v3/api-docs', name: 'Tax' },{ url: 'https://api.pronexus.vn/promotion/v3/api-docs', name: 'Promotion'}, { url: 'https://api.pronexus.vn/webhook/v3/api-docs', name: 'Webhook'}]

# Start all service when run docker compose up
COMPOSE_FILE=docker-compose.yml
COMPOSE_PROJECT_NAME=pvcb-ungluong-backend
# Additional configuration
PRICE_INCLUDES_TAX=false
CURRENCY_UNIT=VND

# OpenAI Config
SPRING_AI_AZURE_OPENAI_API_KEY=
SPRING_AI_AZURE_OPENAI_ENDPOINT=
SPRING_AI_AZURE_OPENAI_EMBEDDING_OPTIONS_MODEL=