package com.salaryadvance.integration.app.responses;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class RecheckRes {
    @JsonProperty(value = "Data")
    private Data data;

    @JsonProperty(value = "Risk")
    private Risk risk;

    @JsonProperty(value = "Meta")
    private Meta meta;

    private ErrorRes errorRes;

    @lombok.Data
    public static class Data{
        @JsonProperty(value = "Status")
        private String status;

        @JsonProperty(value = "Description")
        private String description;

        @JsonProperty(value = "TransId")
        private String transId;

        @JsonProperty(value = "DateTime")
        private String dateTime;

        @JsonProperty(value = "ResponseCode")
        private String responseCode;
    }

    @lombok.Data
    public static class Risk{
        @JsonProperty(value = "BINCode")
        private String bINCode;
    }

    @lombok.Data
    public static class Meta{
        @JsonProperty(value = "RefNum")
        private String refNum;

        @JsonProperty(value = "FtId")
        private String ftId;

        @JsonProperty(value = "SystemTrace")
        private String systemTrace;

        @JsonProperty(value = "TerminalId")
        private String TerminalId;

        @JsonProperty(value = "RefTransDateTime")
        private String refTransDateTime;

        @JsonProperty(value = "NapasStatus")
        private String napasStatus;

        @JsonProperty(value = "NapasRefCode")
        private String napasRefCode;
    }
}
